<template>
  <div class="map-container">
    <div id="map" ref="rootmap">
      <svg-icon icon-class="expend" id="expend-icon"></svg-icon>
    </div>
    <div class="map-table">
      <Table></Table>
    </div>
  </div>
</template>

<script>
import Table from "../Table/Table.vue";
import screenfull from "screenfull";
import Bubble from "./bubble/index.js";
import DragEntity from "./dragentity.js";
import { Positions } from '@/assets/TunnelInfo.js';
import { newPositions, leftPositions, outPositions, rightlinePositions, outPositionsmore1, outPositions2, outPositions3, outPositionsmore3, outPositions4, outPositionsmore4 } from '@/assets/newTunnel.js';
import { getCarPosition, getHisPosition } from '@/api/baseApi/common'
export default {
  name: "DeviceMap",
  props: [],
  components: { Table },
  data() {
    return {
      ViewerMap: null,
      map_baseUrl: "http://************:8090/iserver",
      carSite: [],
      carEntity: {},
      drag: null,
      carPostion: {},
      hisPostion: {},
      flattenedCoords: [],
      flattenedCoords1: [],
      flattenedCoords2: [],
      flattenedCoords3: [],
      flattenedCoords4: [],
      flattenedCoords5: [],
      flattenedCoords6: [],
      flattenedCoords7: [],
      flattenedCoords8: [],
      flattenedCoords9: [],
      flattenedCoords10: [],
      flattenedCoords11: [],
      flattenedCoords12: [],
      flattenedCoords13: [],
      flattenedCoords14: [],
      flattenedCoords15: [],
      flattenedCoords16: [],
      flattenedCoords17: [],
      flattenedCoords18: [],
      flattenedCoords19: [],
      flattenedCoords20: [],
      flattenedCoords21: [],
      flattenedCoords22: [],
      flattenedCoords23: [],
      flattenedCoords24: [],
      flattenedCoords25: [],
      flattenedCoords26: [],
      flattenedCoords27: [],
      flattenedCoords28: [],
      flattenedCoords29: [],
      flattenedCoords30: [],
      flattenedCoords31: [],
      flattenedCoords32: [],
      flattenedCoords33: [],
      flattenedCoords34: [],
      flattenedCoords35: [],
      flattenedCoords36: [],
      flattenedCoords37: [],
      flattenedCoords38: [],
      flattenedCoords39: [],
      flattenedCoords40: [],
      flattenedCoords41: [],
      carPositionInterval: null,
      positionProperty: null,
      startTime: null,
      processedPoints: 0,
      trackedEntity: null
    };
  },

  mounted() {
    // 开发模式：确保必要的状态已设置
    if (!this.$store.state.user.token) {
      this.$store.dispatch('setUserToken', 'dev-token-123');
      this.$store.dispatch('setUserInfo', {
        id: '416132',
        username: 'dev-user',
        name: '开发用户'
      });
      this.$store.dispatch('setUserPermissions', ['admin']);
    }
    if (!this.$store.state.tree.sectionId) {
      this.$store.dispatch('setSection', '1');
    }

    const element = document.getElementById("map"); //指定全屏区域元素
    document.getElementById("expend-icon").addEventListener("click", () => {
      if (screenfull.isEnabled) {
        screenfull.request(element);
      }
      screenfull.toggle();
    }); //实现模块全屏
    this.initMap();
    this.loadpoi();
    this.carPositionInterval = setInterval(() => {
      this.getCarPosition();
    }, 3000);
    // this.getHisPosition();
  },

  beforeDestroy() {
    // 清除定时器
    if (this.carPositionInterval) {
      clearInterval(this.carPositionInterval);
    }
  },

  watch: {
    "$store.state.tree.sectionId": {
      handler: function () {
        // this.drag.remove();
        this.loadMarker();
      },
      // deep: true,
      immediate: true,
    },
  },

  methods: {
    initMap() {
      if (Cesium) {
        this.ViewerMap = new Cesium.Viewer("map", {
          infoBox: false,
          selectionIndicator: false, // 点击实体绿色选择框
          skyAtmosphere: false,
          navigation: false,
          useBrowserRecommendedResolution: true,
        });
        this.ViewerMap._cesiumWidget._creditContainer.style.display = "none"; // 关闭supermap logo

        this.add_TDT_server();
        this.addServer(); // 加载标段信息
        this.loadMarker();

        let viewer = this.ViewerMap;

        // 统一的事件处理器
        this.handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
        let _this = this;

        this.handler.setInputAction(function (movement) {
          const mousePosition = movement.position;
          if (!mousePosition) {
            console.error('Invalid mouse position data in movement.position');
            return;
          }

          // 使用 mousePosition 调用 scene.pick
          const pickedObject = viewer.scene.pick(mousePosition);
          if (Cesium.defined(pickedObject)) {
            // 判断是否是 POI 实体
            if (pickedObject.id === _this.pointEntity) {
              _this.removePOI(_this.ViewerMap, _this.pointEntity);
              _this.clearMarker();
              _this.loadPolyline();
              // _this.loadNewPolyline() 
              _this.loadpoi2();
            }
            else if (pickedObject.id === _this.pointEntity2) {
              _this.reenableEarth()
              _this.add_TDT_server();
              _this.addServer(); // 加载标段信息
              _this.loadMarker();
              _this.ViewerMap.scene.globe.show = true;
              _this.ViewerMap.scene.globe.enableLighting = true;
            }
            else if (pickedObject.id === _this.pointEntity3) {
              _this.removePOI(_this.ViewerMap, _this.pointEntity3);
              _this.clearMarker()
              _this.loadNewPolyline()
              _this.loadrightPolyline()
              _this.loadOutPolyline()
              _this.loadOutPolylinemore1()
              _this.loadOutPolyline2()
              _this.loadOutPolyline3()
              _this.loadOutPolylinemore3()
              _this.loadOutPolyline4()
              _this.loadOutPolylinemore4()
              _this.loadpoi3();
            }
            else if (pickedObject.id === _this.pointEntity4) {
              _this.reenableEarth()
              _this.add_TDT_server();
              _this.addServer(); // 加载标段信息
              _this.loadMarker();
              _this.ViewerMap.scene.globe.show = true;
              _this.ViewerMap.scene.globe.enableLighting = true;
            }
            else if (pickedObject.id && pickedObject.id.id) {
              // 其他实体的点击逻辑
              const id = pickedObject.id.id;
              _this.bubble(id);
            }
          } else {
            // 点击空白区域
            if (_this.bubbles) {
              _this.bubbles.windowClose();
            }
          }
        }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      }
    },

    //加载车辆坐标
    async getCarPosition() {
      let res = await getCarPosition();
      // console.log('getCarPosition:',res)
      if (res.code == 0) {

        this.carPostion = res.result.records
        const filteredCars = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E50PB003282");
        this.flattenedCoords = filteredCars.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);

        const filteredCars1 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007585");
        this.flattenedCoords1 = filteredCars1.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);

        const filteredCars2 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E58NB007596");
        this.flattenedCoords2 = filteredCars2.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars3 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E55NB007572");
        this.flattenedCoords3 = filteredCars3.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars5 = this.carPostion.filter(item => item.factoryNum === "CLG856HEKRL819176");
        this.flattenedCoords5 = filteredCars5.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars6 = this.carPostion.filter(item => item.factoryNum === "CLG856HEPRL819175");
        this.flattenedCoords6 = filteredCars6.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars7 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0035");
        this.flattenedCoords7 = filteredCars7.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars8 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0034");
        this.flattenedCoords8 = filteredCars8.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars9 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0029");
        this.flattenedCoords9 = filteredCars9.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars10 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0028");
        this.flattenedCoords10 = filteredCars10.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars11 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0030");
        this.flattenedCoords11 = filteredCars11.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        // const filteredCars12 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007585");
        // this.flattenedCoords12 = filteredCars12.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars13 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E5XNB007583");
        // this.flattenedCoords13 = filteredCars13.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars14 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E55NB007524");
        // this.flattenedCoords14 = filteredCars14.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars15 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007571");
        // this.flattenedCoords15 = filteredCars15.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars16 = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E58PB003286");
        // this.flattenedCoords16 = filteredCars16.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        const filteredCars17 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007604");
        this.flattenedCoords17 = filteredCars17.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars18 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E58NB007596");
        this.flattenedCoords18 = filteredCars18.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        // const filteredCars19 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E52NB007576");
        // this.flattenedCoords19 = filteredCars19.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        const filteredCars20 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E57NB007573");
        this.flattenedCoords20 = filteredCars20.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars21 = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E5XPB003287");
        this.flattenedCoords21 = filteredCars21.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars22 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E51NB007519");
        this.flattenedCoords22 = filteredCars22.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars23 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E54NB007577");
        this.flattenedCoords23 = filteredCars23.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars24 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E59NB007574");
        this.flattenedCoords24 = filteredCars24.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars25 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E55NB007572");
        this.flattenedCoords25 = filteredCars25.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars26 = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E50PB003282");
        this.flattenedCoords26 = filteredCars26.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        const filteredCars27 = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E54PB003284");
        this.flattenedCoords27 = filteredCars27.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        // const filteredCars28 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E57NB007525");
        // this.flattenedCoords28 = filteredCars28.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars29 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E50NB007575");
        // this.flattenedCoords29 = filteredCars29.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars30 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E58NB007579");
        // this.flattenedCoords30 = filteredCars30.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars31 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E56NB007595");
        // this.flattenedCoords31 = filteredCars31.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars32 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E51NB007522");
        // this.flattenedCoords32 = filteredCars32.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars33 = this.carPostion.filter(item => item.factoryNum === "LZ5NA9E56PB003285");
        // this.flattenedCoords33 = filteredCars33.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars34 = this.carPostion.filter(item => item.factoryNum === "24KJ2NHK-0090");
        // this.flattenedCoords34 = filteredCars34.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        console.log("LZ5NA9E50PB003282", this.flattenedCoords)
        console.log("LZ5NA7E53NB007585", this.flattenedCoords1)
        // alert(this.flattenedCoords34)
        // console.log("LZ5NA7E58NB007596", this.flattenedCoords2)
        // console.log("LZ5NA7E55NB007572", this.flattenedCoords3)

      }
    },

    async getHisPosition() {
      let res = await getHisPosition();
      console.log(res)
      if (res.code == 0) {
        this.hisPostion = res.result.records
        // const filteredCars4 = this.hisPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007604");
        this.flattenedCoords4 = this.hisPostion.flatMap(item => [
          Number(item.lat),
          Number(item.lon)
        ]);
        // const filteredCars5 = this.hisPostion.filter(item => item.factoryNum === "LZ5NA7E53NB007604");
        // this.flattenedCoords5 = filteredCars5.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars6 = this.hisPostion.filter(item => item.factoryNum === "LZ5NA7E58NB007596");
        // this.flattenedCoords6 = filteredCars6.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // const filteredCars7 = this.carPostion.filter(item => item.factoryNum === "LZ5NA7E55NB007572");
        // this.flattenedCoords7 = filteredCars7.flatMap(item => [
        //   Number(item.lat),
        //   Number(item.lon)
        // ]);
        // console.log("历史", this.flattenedCoords4)
        // console.log("HisLZ5NA7E53NB007604",this.flattenedCoords5)
        // console.log("HisLZ5NA7E58NB007596",this.flattenedCoords6)
        // console.log("HisLZ5NA7E55NB007572",this.flattenedCoords7)
        // console.log("历史数据",this.hisPostion)


      }
    },

    // 加载车辆标注
    async loadMarker() {
      let res = await this.$http.get(
        `/equip/build/map?sectionId=${this.$store.state.tree.sectionId || ''}`
      );
      // console.log("Mapdata:",res);

      if (res.result && res.result.length > 0) {
        this.carSite = res?.result || [];
        this.dragEntity(); // 添加实体
      }
    },

    // 弹窗模板
    bubble(id) {
      if (this.bubbles) {
        this.bubbles.windowClose();
      }
      // console.log("id:", this.carEntity[id]);

      this.bubbles = new Bubble(
        Object.assign(this.carEntity[id], {
          viewer: this.ViewerMap,
        })
      );
      // console.log("bubbles:", this.bubbles);

    },

    //加载实体
    dragEntity() {
      this.drag = new DragEntity({
        viewer: this.ViewerMap,
      });
      let _this = this;

      // console.log(_this.carSite);

      _this.carSite.forEach((item) => {
        // console.log("item:",!Object.hasOwn(_this.carEntity, item.id) && item.lon && item.lat);
        if (!Object.hasOwn(_this.carEntity, item.id) && item.lon && item.lat) {
          let entity = _this.drag.addEntity(item);
          console.log("entity:", entity);
          _this.carEntity[item.id] = entity;
        }
        // _this.carEntity[item.id] = entity;


      });
      // console.log("carEntity:");

      // this.carSite.forEach((item) => {

      //   let entity = this.drag.addEntity(item);
      //   this.carEntity[item.id] = entity;
      // });

    },

    // 天地图矢量地图
    add_TDT_server() {
      Cesium.Ion.defaultAccessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"; // 自己申请 或者放在全局变量 或者挂载原型上调用
      // vec(矢量)、img(影像)、cia(影像中文注记)、cva(矢量中文注记)
      var layer = new Cesium.WebMapTileServiceImageryProvider({
        url: "http://t0.tianditu.gov.cn/img_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        layer: "img",
        style: "default",
        tileMatrixSetID: "w",
        format: "tiles",
        maximumLevel: 18, // 必须加上最大级数
      });

      this.ViewerMap.imageryLayers.addImageryProvider(layer);
      var layer0 = new Cesium.WebMapTileServiceImageryProvider({
        url: "http://t0.tianditu.gov.cn/vec_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        layer: "vec",
        style: "default",
        tileMatrixSetID: "w",
        format: "tiles",
        maximumLevel: 18, // 必须加上最大级数
      });

      this.ViewerMap.imageryLayers.addImageryProvider(layer0);
      // 加载影像注记
      var layer1 = new Cesium.WebMapTileServiceImageryProvider({
        url: "http://t0.tianditu.gov.cn/cia_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        layer: "cia",
        style: "default",
        tileMatrixSetID: "w",
        format: "tiles",
        maximumLevel: 18,
      });
      this.ViewerMap.imageryLayers.addImageryProvider(layer1);
      var layer2 = new Cesium.WebMapTileServiceImageryProvider({
        url: "http://t0.tianditu.gov.cn/cva_w/wmts?tk=b6e16a5ffc7b6e167bef81496433f1c9",
        subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"],
        layer: "cva",
        style: "default",
        tileMatrixSetID: "w",
        format: "tiles",
        maximumLevel: 18,
      });
      this.ViewerMap.imageryLayers.addImageryProvider(layer2);
      this.ViewerMap.imageryLayers.get(0).show = false;
      this.ViewerMap.imageryLayers.get(1).show = false;
      this.flyTo(null, 104.06585, 30.657361, 800000, 0.0, -0.0, 0); // 成都
      // this.flyTo(null, 116.693075, 35.440788, 800000, 0.0, -0.0, 0)
    },

    // 线路服务
    addServer() {
      this.ViewerMap.imageryLayers.addImageryProvider(
        new Cesium.SuperMapImageryProvider({
          url: this.map_baseUrl + "/services/map-station/rest/maps/station",
        })
      );
    },
    //删除poi
    removePOI(viewer, poiEntity) {
      if (viewer && poiEntity) {
        viewer.entities.remove(poiEntity);
        console.log("POI removed successfully.");
      } else {
        console.error("Invalid viewer or POI entity.");
      }
    },
    //加载初始poi
    loadpoi() {
      console.log('开始加载POI点...');
      if (!this.ViewerMap) {
        console.error('ViewerMap未初始化，无法加载POI');
        return;
      }

      const position = Cesium.Cartesian3.fromDegrees(101.**********, 29.**********);
      const position3 = Cesium.Cartesian3.fromDegrees(101.44216945, 30.07210385);
      const _textColor = "rgb(11,255,244)";

      console.log('创建POI实体...');
      this.pointEntity = this.ViewerMap.entities.add({
        position: position,
        billboard: {
          image: '/normal.png', // 图片路径
          width: 120,
          height: 200,
          scale: 1.0,
          color: Cesium.Color.WHITE,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        },
        label: {
          text: 'cz7',
          font: "20px monospace",
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          fillColor: new Cesium.Color.fromCssColorString(_textColor),
          outlineWidth: 4,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -85),
        }
      });
      this.pointEntity3 = this.ViewerMap.entities.add({
        position: position3,
        billboard: {
          image: '/normal.png', // 图片路径
          width: 120,
          height: 200,
          scale: 1.0,
          color: Cesium.Color.WHITE,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        },
        label: {
          text: '高尔寺隧道正洞',
          font: "20px monospace",
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          fillColor: new Cesium.Color.fromCssColorString(_textColor),
          outlineWidth: 4,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -85),
        }
      });



    },
    //加载隐藏poi
    loadpoi2() {
      const position1 = Cesium.Cartesian3.fromDegrees(101.8806010795, 29.9926789909, 3216.842687);
      this.pointEntity2 = this.ViewerMap.entities.add({
        position: position1,
        billboard: {
          image: '/normal.png', // 图片路径
          width: 80,
          height: 100,
          scale: 1.0,
          color: Cesium.Color.WHITE,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        }
      });
    },
    loadpoi3() {
      const position1 = Cesium.Cartesian3.fromDegrees(101.44216945, 30.07210385, 0);
      this.pointEntity4 = this.ViewerMap.entities.add({
        position: position1,
        billboard: {
          image: '/normal.png', // 图片路径
          width: 80,
          height: 110,
          scale: 1.0,
          color: Cesium.Color.WHITE,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM
        }
      });
    },
    //加载人车模型
    loadGltf() {
      if (!this.ViewerMap) {
        console.error('ViewerMap is not initialized!');
        return;
      }

      const modelPosition = Cesium.Cartesian3.fromDegrees(
        101.8291053377, 30.0025819052, 3320.977687
      );
      const modelPosition2 = Cesium.Cartesian3.fromDegrees(
        101.875012616, 29.994756921, 3249.977687
      );
      const modelPosition3 = Cesium.Cartesian3.fromDegrees(
        101.8489119221, 30.0016986489, 3291.6308
      );

      const entity = this.ViewerMap.entities.add({
        position: modelPosition,
        model: {
          uri: '/wheel_loader.glb', // GLTF模型的路径
          scale: 0.5, // 模型缩放比例
          minimumPixelSize: 10, // 模型的最小像素大小
          maximumScale: 20 // 模型的最大缩放比例
        }
      });
      const entity1 = this.ViewerMap.entities.add({
        position: modelPosition2,
        model: {
          uri: '/a_chinese_worker.glb', // GLTF模型的路径
          scale: 0.005, // 模型缩放比例
          minimumPixelSize: 135, // 模型的最小像素大小
          maximumScale: 135 // 模型的最大缩放比例
        }
      });
      const entity2 = this.ViewerMap.entities.add({
        position: modelPosition3,
        model: {
          uri: '/a_chinese_worker.glb', // GLTF模型的路径
          scale: 0.005, // 模型缩放比例
          minimumPixelSize: 135, // 模型的最小像素大小
          maximumScale: 135 // 模型的最大缩放比例
        }
      });


    },

    loadGltf2() {
      if (!this.ViewerMap) {
        console.error('ViewerMap is not initialized!');
        return;
      }

      // 初始位置

      let modelPosition = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords[1], this.flattenedCoords[0], 0
      );


      const entity4 = this.ViewerMap.entities.add({
        position: modelPosition1,
        model: {
          uri: '/a_chinese_worker.glb',
          scale: 10,
          minimumPixelSize: 150,
          maximumScale: 200
        },
        label: {
          text: `车辆LZ5NA9E50PB003282`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });

      // const positionProperty = new Cesium.SampledPositionProperty();

      // // 设置时钟参数 - 无限循环
      // const startTime = Cesium.JulianDate.now();
      // const totalSeconds = (this.flattenedCoords4.length / 2) * 5; // 总运动时间
      // const stopTime = Cesium.JulianDate.addSeconds(
      //   startTime,
      //   totalSeconds,
      //   new Cesium.JulianDate()
      // );

      // this.ViewerMap.clock.startTime = startTime.clone();
      // this.ViewerMap.clock.stopTime = stopTime.clone();
      // this.ViewerMap.clock.currentTime = startTime.clone();
      // this.ViewerMap.clock.clockRange = Cesium.ClockRange.LOOP; // 改为LOOP实现无限循环
      // this.ViewerMap.clock.multiplier = 1; // 实时速度

      // // 添加路径点
      // for (let i = 0; i < this.flattenedCoords4.length; i += 2) {
      //   const time = Cesium.JulianDate.addSeconds(
      //     startTime,
      //     (i / 2) * 5, // 每个点间隔5秒
      //     new Cesium.JulianDate()
      //   );

      //   const position = Cesium.Cartesian3.fromDegrees(
      //     this.flattenedCoords4[i],
      //     this.flattenedCoords4[i + 1],
      //     0
      //   );

      //   positionProperty.addSample(time, position);
      // }

      // // 绑定路径和方向
      // entity4.position = positionProperty;
      // entity4.orientation = new Cesium.VelocityOrientationProperty(positionProperty);

      // this.ViewerMap.clock.shouldAnimate = true;

      // // 跟踪相机（可选）
      // this.ViewerMap.trackedEntity = entity4;

      // // 添加循环结束事件监听（可选）
      // this.ViewerMap.clock.onTick.addEventListener(() => {
      //   if (Cesium.JulianDate.compare(this.ViewerMap.clock.currentTime, stopTime) >= 0) {
      //     console.log('路径循环完成，重新开始');
      //   }
      // });
    },
    loadRealGltf2() {
      if (!this.ViewerMap) {
        console.error('ViewerMap is not initialized!');
        return;
      }

      // 初始位置（起点）
      let modelPosition = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords[1], this.flattenedCoords[0], 0
      );
      // 添加小车模型
      const entity4 = this.ViewerMap.entities.add({
        position: modelPosition,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA9E50PB003282`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });

      const positionProperty = new Cesium.SampledPositionProperty();

      // 设置时钟参数 - 不循环，停在终点
      const startTime = Cesium.JulianDate.now();
      const totalSeconds = (this.flattenedCoords.length / 2) * 3; // 总运动时间
      const stopTime = Cesium.JulianDate.addSeconds(
        startTime,
        totalSeconds,
        new Cesium.JulianDate()
      );

      this.ViewerMap.clock.startTime = startTime.clone();
      this.ViewerMap.clock.stopTime = stopTime.clone();
      this.ViewerMap.clock.currentTime = startTime.clone();
      this.ViewerMap.clock.clockRange = Cesium.ClockRange.CLAMPED; // 关键修改：改为CLAMPED，时间到达终点后停止
      this.ViewerMap.clock.multiplier = 1; // 实时速度

      // 添加路径点
      for (let i = 0; i < this.flattenedCoords.length; i += 2) {
        const time = Cesium.JulianDate.addSeconds(
          startTime,
          (i / 2) * 3, // 每个点间隔3秒
          new Cesium.JulianDate()
        );

        const position = Cesium.Cartesian3.fromDegrees(
          this.flattenedCoords[i],
          this.flattenedCoords[i + 1],
          0
        );

        positionProperty.addSample(time, position);
      }

      // 添加一个额外的终点样本作为保险
      const finalPosition = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords[this.flattenedCoords.length - 2],
        this.flattenedCoords[this.flattenedCoords.length - 1],
        0
      );
      positionProperty.addSample(stopTime, finalPosition);

      // 绑定路径和方向
      entity4.position = positionProperty;
      entity4.orientation = new Cesium.VelocityOrientationProperty(positionProperty);

      this.ViewerMap.clock.shouldAnimate = true;

      // 跟踪相机（可选）
      this.ViewerMap.trackedEntity = entity4;

      // 添加时间到达终点的监听（可选）
      this.ViewerMap.clock.onTick.addEventListener(() => {
        if (Cesium.JulianDate.compare(this.ViewerMap.clock.currentTime, stopTime) >= 0) {
          console.log('车辆已到达终点并停止');
          // 确保位置设置为终点
          entity4.position = finalPosition;
          // 停止方向更新
          entity4.orientation = undefined;
        }
      });
    },
    loadNomoveCar() {
      if (!this.ViewerMap) {
        console.error('ViewerMap is not initialized!');
        return;
      }

      // 初始位置（起点）
      let modelPosition1 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords1[1], this.flattenedCoords1[0], 0
      );
      let modelPosition2 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords2[1], this.flattenedCoords2[0], 0
      );
      let modelPosition3 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords3[1], this.flattenedCoords3[0], 0
      );
      let modelPosition5 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords5[1], this.flattenedCoords5[0], 0
      );
      let modelPosition6 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords6[1], this.flattenedCoords6[0], 0
      );
       let modelPosition7 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords7[1], this.flattenedCoords7[0], 0
      );
      let modelPosition8 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords8[1], this.flattenedCoords8[0], 0
      );
      let modelPosition9 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords9[1], this.flattenedCoords9[0], 0
      );
      let modelPosition10 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords10[1], this.flattenedCoords10[0], 0
      );
       let modelPosition11 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords11[1], this.flattenedCoords11[0], 0
      );
       let modelPosition17 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords17[1], this.flattenedCoords17[0], 0
      );
       let modelPosition18 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords18[1], this.flattenedCoords18[0], 0
      );
       let modelPosition20 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords20[1], this.flattenedCoords20[0], 0
      );
      let modelPosition21 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords21[1], this.flattenedCoords21[0], 0
      );
      let modelPosition22 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords22[1], this.flattenedCoords22[0], 0
      );
      let modelPosition23 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords23[1], this.flattenedCoords23[0], 0
      );
       let modelPosition24 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords24[1], this.flattenedCoords24[0], 0
      );
       let modelPosition25 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords25[1], this.flattenedCoords25[0], 0
      );
       let modelPosition27 = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords27[1], this.flattenedCoords27[0], 0
      );
      // let modelPosition12 = Cesium.Cartesian3.fromDegrees(
      //   this.flattenedCoords12[1], this.flattenedCoords12[0], 0
      // );
      let modelPosition = Cesium.Cartesian3.fromDegrees(
        this.flattenedCoords[1], this.flattenedCoords[0], 0
      );
      // 添加小车模型
      const entity4 = this.ViewerMap.entities.add({
        position: modelPosition,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA9E50PB003282`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      // 添加小车模型
      const entity5 = this.ViewerMap.entities.add({
        position: modelPosition1,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E53NB007585`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.YELLOW,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity6 = this.ViewerMap.entities.add({
        position: modelPosition2,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E58NB007596`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity7 = this.ViewerMap.entities.add({
        position: modelPosition3,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E55NB007572`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity8 = this.ViewerMap.entities.add({
        position: modelPosition5,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆CLG856HEKRL819176`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity9 = this.ViewerMap.entities.add({
        position: modelPosition6,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆CLG856HEPRL819175`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity10 = this.ViewerMap.entities.add({
        position: modelPosition7,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆24KJ2NHK-0035`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity11 = this.ViewerMap.entities.add({
        position: modelPosition8,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆24KJ2NHK-0034`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity12 = this.ViewerMap.entities.add({
        position: modelPosition9,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆24KJ2NHK-0029`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity13 = this.ViewerMap.entities.add({
        position: modelPosition10,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆24KJ2NHK-0028`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity14 = this.ViewerMap.entities.add({
        position: modelPosition11,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆24KJ2NHK-0030`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity15 = this.ViewerMap.entities.add({
        position: modelPosition17,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E53NB007604`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
       const entity16 = this.ViewerMap.entities.add({
        position: modelPosition18,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E58NB007596`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
       const entity17 = this.ViewerMap.entities.add({
        position: modelPosition20,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E57NB007573`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity18 = this.ViewerMap.entities.add({
        position: modelPosition21,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA9E5XPB003287`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity19 = this.ViewerMap.entities.add({
        position: modelPosition22,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E51NB007519`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
       const entity20 = this.ViewerMap.entities.add({
        position: modelPosition23,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E54NB007577`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity21 = this.ViewerMap.entities.add({
        position: modelPosition24,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E59NB007574`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity22 = this.ViewerMap.entities.add({
        position: modelPosition25,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA7E55NB007572`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
      const entity23 = this.ViewerMap.entities.add({
        position: modelPosition27,
        model: {
          uri: '/wheel_loader.glb',
          scale: 1.5,
          minimumPixelSize: 10,
          maximumScale: 20
        },
        label: {
          text: `车辆LZ5NA9E54PB003284`,
          font: '14px sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
    },
    loadCarModel() {
      // 创建动态位置属性
      this.positionProperty = new Cesium.SampledPositionProperty();
      this.startTime = Cesium.JulianDate.now();

      // 添加小车模型实体
      this.trackedEntity = this.ViewerMap.entities.add({
        name: 'Moving Car',
        position: this.positionProperty,
        model: {
          uri: '/wheel_loader.glb',
          scale: 10,
          minimumPixelSize: 150,
          maximumScale: 200
        },
        path: {
          resolution: 1,
          material: new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.2,
            color: Cesium.Color.YELLOW
          }),
          width: 10,
          leadTime: 0,
          trailTime: 60 // 显示60秒的轨迹
        }
      });
      // 设置模型方向跟随移动方向
      this.trackedEntity.orientation = new Cesium.VelocityOrientationProperty(
        this.positionProperty
      );

      // 设置相机跟踪
      this.ViewerMap.trackedEntity = this.trackedEntity;

      // 初始化时钟
      this.ViewerMap.clock.startTime = this.startTime.clone();
      this.ViewerMap.clock.currentTime = this.startTime.clone();
      this.ViewerMap.clock.shouldAnimate = true;
    },
    startTracking() {
      // 初始加载小车模型
      this.loadCarModel();
      // 开始定时获取位置
      this.getCarPosition();
      this.carPositionInterval = setInterval(() => {
        this.getCarPosition();
      }, 3000);
    },
    updatePath() {
      if (!this.positionProperty || !this.startTime) return;

      const totalPoints = this.flattenedCoords.length / 2;

      // 只处理新增的点
      for (let i = this.processedPoints; i < totalPoints; i++) {
        const lon = this.flattenedCoords[i * 2];
        const lat = this.flattenedCoords[i * 2 + 1];

        // 计算对应的时间点（每点间隔3秒）
        const time = Cesium.JulianDate.addSeconds(
          this.startTime,
          i * 3,
          new Cesium.JulianDate()
        );

        // 添加位置样本
        const position = Cesium.Cartesian3.fromDegrees(lon, lat, 0);
        this.positionProperty.addSample(time, position);
      }

      // 更新已处理点数
      this.processedPoints = totalPoints;

      // 更新时钟的停止时间
      const newStopTime = Cesium.JulianDate.addSeconds(
        this.startTime,
        totalPoints * 3,
        new Cesium.JulianDate()
      );
      this.ViewerMap.clock.stopTime = newStopTime;

      // 自动跳转到最新时间（可选）
      this.ViewerMap.clock.currentTime = newStopTime;
    },

    //加载隧道模型
    loadPolyline() {
      if (this.ViewerMap) {

        this.ViewerMap.scene.globe.show = false;
        // 禁用地球表面的光照
        this.ViewerMap.scene.globe.enableLighting = false;
        this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < Positions.length; i += 3) {
          const position = Cesium.Cartesian3.fromDegrees(
            Positions[i],
            Positions[i + 1],
            Positions[i + 2]
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //     polyline: {
        //         positions: positions,
        //         width: 5,
        //         material: Cesium.Color.RED
        //     }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 1600;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 500; // 左边200米
        const leftBufferHeight = 5;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 500; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  1000,  // 当相机距离为1000米时
                  1.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  0.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 1000;
        const innerRadius = 950;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: new Cesium.Color(1, 1, 1, 0.2), // 透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        this.ViewerMap.flyTo(halfTube);
      }
    },
    // 左线隧道
    loadNewPolyline() {
      if (this.ViewerMap) {
        this.ViewerMap.scene.globe.show = false;
        // 禁用地球表面的光照
        this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf2();//历史轨迹
        // this.loadRealGltf2();
        this.loadNomoveCar();
        this.startTracking();
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < leftPositions.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            leftPositions[i],
            leftPositions[i + 1],
            0 // 高度设置为0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //     polyline: {
        //         positions: positions,
        //         width: 5,
        //         material: Cesium.Color.WHITE
        //     }
        // });

        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 5; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 5; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 16;
        const innerRadius = 12;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        this.ViewerMap.flyTo(halfTube);
      }
    },
    // 一号斜井
    loadOutPolyline() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositions.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositions[i],
            outPositions[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 一号斜井补充
    loadOutPolylinemore1() {
      console.log('开始执行 loadOutPolylinemore1...');

      if (!this.ViewerMap) {
        console.error('ViewerMap未初始化，无法加载隧道模型');
        return;
      }

      console.log('outPositionsmore1 数据:', outPositionsmore1);

      // this.ViewerMap.scene.globe.show = false;
      // // 禁用地球表面的光照
      // this.ViewerMap.scene.globe.enableLighting = false;
      // this.loadGltf()
      const positions = [];
      const distances = [0]; // 初始化距离数组，第一个点的距离为0

      // 生成路径点（与polyline共用）
      for (let i = 0; i < outPositionsmore1.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositionsmore1[i],
            outPositionsmore1[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 二号斜井
    loadOutPolyline2() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositions2.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositions2[i],
            outPositions2[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 三号斜井
    loadOutPolyline3() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositions3.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositions3[i],
            outPositions3[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 三号斜井补充
    loadOutPolylinemore3() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositionsmore3.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositionsmore3[i],
            outPositionsmore3[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 四号斜井
    loadOutPolyline4() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositions4.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositions4[i],
            outPositions4[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 四号斜井补充
    loadOutPolylinemore4() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < outPositionsmore4.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            outPositionsmore4[i],
            outPositionsmore4[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 10; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 10; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            this.ViewerMap.entities.add({
              position: labelPosition,
              label: {
                text: `DK${currentMileage}`,
                font: '14px sans-serif',
                fillColor: Cesium.Color.WHITE,
                outlineColor: Cesium.Color.BLACK,
                outlineWidth: 2,
                style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, -10),
                // 添加距离显示条件，控制标签在什么距离范围内显示
                distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
                  0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
                  12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
                ),
                // 可选：根据相机距离动态调整标签大小
                scaleByDistance: new Cesium.NearFarScalar(
                  2000,  // 当相机距离为1000米时
                  2.0,   // 标签大小为1.0倍
                  12000,  // 当相机距离为5000米时
                  1.5    // 标签缩小到0.5倍
                )
              }
            });

            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 20;
        const innerRadius = 15;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 右线隧道
    loadrightPolyline() {
      if (this.ViewerMap) {

        // this.ViewerMap.scene.globe.show = false;
        // // 禁用地球表面的光照
        // this.ViewerMap.scene.globe.enableLighting = false;
        // this.loadGltf()
        const positions = [];
        const distances = [0]; // 初始化距离数组，第一个点的距离为0

        // 生成路径点（与polyline共用）
        for (let i = 0; i < rightlinePositions.length; i += 2) {
          const position = Cesium.Cartesian3.fromDegrees(
            rightlinePositions[i],
            rightlinePositions[i + 1],
            0
          );
          positions.push(position);

          // 计算相邻点之间的距离并累加
          if (positions.length > 1) {
            const prevPosition = positions[positions.length - 2];
            const distance = Cesium.Cartesian3.distance(prevPosition, position);
            distances.push(distances[distances.length - 1] + distance);
          }
        }

        // 添加红色引导线
        // this.ViewerMap.entities.add({
        //   polyline: {
        //     positions: positions,
        //     width: 5,
        //     material: Cesium.Color.RED
        //   }
        // });


        // // 生成半圆形截面形状函数
        const computeHalfRing = (outerRadius, innerRadius) => {
          const points = [];
          const angleStep = 180; // 180度表示半圆

          // 外圆（顺时针方向生成半圆）
          for (let i = 0; i <= angleStep; i++) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              outerRadius * Math.cos(radians),
              outerRadius * Math.sin(radians)
            ));
          }

          // 内圆（逆时针方向生成半圆，闭合空心部分）
          for (let i = angleStep; i >= 0; i--) {
            const radians = Cesium.Math.toRadians(i);
            points.push(new Cesium.Cartesian2(
              innerRadius * Math.cos(radians),
              innerRadius * Math.sin(radians)
            ));
          }

          return points;
        };

        const computeRectangle = (width, height) => {
          const halfWidth = width / 2;
          const halfHeight = height / 2;
          return [
            new Cesium.Cartesian2(-halfWidth, -halfHeight), // 左下角
            new Cesium.Cartesian2(halfWidth, -halfHeight),  // 右下角
            new Cesium.Cartesian2(halfWidth, halfHeight),   // 右上角
            new Cesium.Cartesian2(-halfWidth, halfHeight)  // 左上角
          ];
        };

        // 缓冲区宽度为800米（左右各400米）
        const bufferWidth = 24;
        const bufferHeight = 1;
        const buffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(bufferWidth, bufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 绿色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });

        const leftBufferWidth = 5; // 左边200米
        const leftBufferHeight = 1;
        const leftBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(leftBufferWidth, leftBufferHeight), // 生成矩形截面
            material: new Cesium.Color(1, 1, 1, 0.5), // 灰色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const MidBufferWidth = 5; // 左边200米
        const MidBufferHeight = 1;
        const MidBuffer = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeRectangle(MidBufferWidth, MidBufferHeight), // 生成矩形截面
            material: Cesium.Color.YELLOW.withAlpha(0.2), // 黄色半透明效果
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
        const startMileage = 28800; // 起始里程DK28800
        const mileageInterval = 1000; // 每100米标注一次

        // 计算两点之间的距离
        function calculateDistance(point1, point2) {
          return Cesium.Cartesian3.distance(point1, point2);
        }

        // 计算累计距离并标注
        let accumulatedDistance = 0; // 累计距离
        let currentMileage = startMileage; // 当前里程

        for (let i = 1; i < positions.length; i++) {
          const distance = calculateDistance(positions[i - 1], positions[i]);
          accumulatedDistance += distance;

          // 如果累计距离超过100米，标注里程
          while (accumulatedDistance >= mileageInterval) {
            // 计算标注点的位置
            const fraction = (mileageInterval - (accumulatedDistance - distance)) / distance;
            const labelPosition = Cesium.Cartesian3.lerp(positions[i - 1], positions[i], fraction, new Cesium.Cartesian3());

            // 添加标注
            // this.ViewerMap.entities.add({
            //   position: labelPosition,
            //   label: {
            //     text: `DK${currentMileage}`,
            //     font: '14px sans-serif',
            //     fillColor: Cesium.Color.WHITE,
            //     outlineColor: Cesium.Color.BLACK,
            //     outlineWidth: 2,
            //     style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            //     verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            //     pixelOffset: new Cesium.Cartesian2(0, -10),
            //     // 添加距离显示条件，控制标签在什么距离范围内显示
            //     distanceDisplayCondition: new Cesium.DistanceDisplayCondition(
            //       0,    // 最小可见距离（米），0表示相机距离小于这个值时可见
            //       12000 // 最大可见距离（米），相机距离大于这个值时标签会隐藏
            //     ),
            //     // 可选：根据相机距离动态调整标签大小
            //     scaleByDistance: new Cesium.NearFarScalar(
            //       2000,  // 当相机距离为1000米时
            //       2.0,   // 标签大小为1.0倍
            //       12000,  // 当相机距离为5000米时
            //       1.5    // 标签缩小到0.5倍
            //     )
            //   }
            // });
            // 更新累计距离和当前里程
            accumulatedDistance -= mileageInterval;
            currentMileage += mileageInterval;
          }
        }
        // 创建半圆形隧道（半圆柱体）
        const outerRadius = 16;
        const innerRadius = 12;
        const halfTube = this.ViewerMap.entities.add({
          polylineVolume: {
            positions: positions, // 使用与polyline相同的路径
            shape: computeHalfRing(outerRadius, innerRadius),
            material: Cesium.Color.BLUE.withAlpha(0.5),
            cornerType: Cesium.CornerType.ROUNDED
          }
        });
      }
    },
    // 地球初始化
    reenableEarth() {
      // let scene = this.this.ViewerMap.scene;
      // scene.globe.show = true;
      // scene.sun.show = true;
      // scene.moon.show = true;
      // scene.skyBox.show = true;
      // scene.fog.enabled = true;
      let viewer = this.ViewerMap;
      viewer.scene.globe.show = true;
      viewer.scene.camera.lookAtTransform(Cesium.Matrix4.IDENTITY)
      viewer.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(104.06585, 30.657361, 800000),
        // orientation: {
        //   heading: Cesium.Math.toRadians(180.000),
        //   pitch: Cesium.Math.toRadians(-89.2641148),
        //   roll: 348.6959
        // }
      });
      this.clearPolyline(viewer)
      this.loadpoi();
    },
    // 清空隧道
    clearPolyline(viewer) {
      if (viewer) {
        viewer.entities.removeAll();
        viewer.scene.globe.show = true;
        viewer.scene.globe.enableLighting = true;
        console.log("All polylines, buffers, and labels have been removed.");
      } else {
        console.error("Invalid viewer.");
      }
    },
    clearMarker() {
      if (this.drag && this.drag.viewer) {
        // 假设DragEntity类有clearAll方法
        if (typeof this.drag.clearAll === 'function') {
          this.drag.clearAll();
        }
        // 如果没有clearAll方法，则遍历移除
        else {
          for (const id in this.carEntity) {
            this.ViewerMap.entities.remove(this.carEntity[id]);
          }
        }
      }
      this.carEntity = {};
      this.carSite = [];
      this.drag = null;
    },
    // 移动视口 有动画3s
    flyTo(cb, longitude, latitude, height, heading, pitch, roll) {
      this.ViewerMap.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          parseFloat(longitude),
          parseFloat(latitude),
          parseFloat(height)
        ),
        orientation: {
          heading: heading ? Cesium.Math.toRadians(heading) : null,
          pitch: pitch ? Cesium.Math.toRadians(pitch) : null,
          roll: roll ? Cesium.Math.toRadians(roll) : null,
        },
        complete: () => {
          cb && cb();
        },
        cancel() {
          cb && cb();
        },
      });
    },
  }

</script>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 100%;

  #map {
    width: 100%;
    height: 70%;
    position: relative;

    #expend-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 999;
      cursor: pointer;
    }
  }

  .map-table {
    width: 100%;
    height: 29%;
    margin-top: 1%;
    overflow: hidden;
  }
}
</style>
